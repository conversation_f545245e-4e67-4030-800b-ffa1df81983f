package com.heal.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.GetCompInstance;
import com.heal.controlcenter.pojo.HostInstanceDetails;
import com.heal.controlcenter.util.PaginationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Repository
public class ComponentInstanceDao {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Constructor for dependency injection.
     * @param jdbcTemplate Spring JDBC template for database operations
     */
    public ComponentInstanceDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<ViewComponentInstanceBean> getActiveInstanceDetailsForAccount(int accountId) {
        String query = "SELECT id, name, identifier, host_id AS hostId, host_name AS hostName, is_cluster AS isCluster, " +
                "discovery, host_address AS hostAddress, is_DR AS isDR, " +
                "mst_component_id AS mstComponentId, component_name AS mstComponentName, " +
                "mst_component_type_id AS mstComponentTypeId, component_type_name AS mstComponentTypeName, " +
                "mst_component_version_id AS mstComponentVersionId, component_version_name AS componentVersionName, " +
                "common_version_id AS commonVersionId, common_version_name AS commonVersionName, " +
                "updated_time AS updatedTime " +
                "FROM view_component_instance vci " +
                "WHERE account_id = ? AND status = 1 AND is_cluster = 0";

        try {
            log.debug("Fetching active instance details for accountId: {}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewComponentInstanceBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching active instance details for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    public List<ViewClusterServicesBean> getAllClusterServices() {
        String query = "SELECT id AS clusterId, name AS clusterName, identifier AS clusterIdentifier, " +
                "host_cluster_id AS hostClusterId, mst_component_id AS mstComponentId, " +
                "mst_component_type_id AS mstComponentTypeId, mst_component_version_id AS mstComponentVersionId, " +
                "service_id AS serviceId, service_name AS serviceName, service_identifier AS serviceIdentifier " +
                "FROM view_cluster_services";

        try {
            log.debug("Fetching all cluster services");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewClusterServicesBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching cluster services. Details:", e);
            return Collections.emptyList();
        }
    }

    public List<ViewApplicationServiceMappingBean> getAllServiceApplication(int accountId) {
        String query = "SELECT application_id AS applicationId, " +
                "application_name AS applicationName, " +
                "application_identifier AS applicationIdentifier, " +
                "service_id AS serviceId, " +
                "service_name AS serviceName, " +
                "service_identifier AS serviceIdentifier, " +
                "account_id AS accountId " +
                "FROM view_application_service_mapping " +
                "WHERE account_id = ?";

        try {
            log.debug("Fetching all service-application mappings for accountId: {}", accountId);
            return jdbcTemplate.query(query,
                    new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class),
                    accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching service-application mappings for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    public List<CompInstanceAttributesBean> getInstAttributeMapping(String attributeName) {
        String query = "SELECT ciav.id, ciav.attribute_value AS attributeValue, " +
                "ciav.comp_instance_id AS compInstanceId, " +
                "ciav.mst_component_attribute_mapping_id AS mstComponentAttributeMappingId, " +
                "ciav.mst_common_attributes_id AS mstCommonAttributesId, " +
                "ciav.attribute_name AS attributeName " +
                "FROM comp_instance_attribute_values ciav, mst_common_attributes mca " +
                "WHERE ciav.mst_common_attributes_id = mca.id AND mca.attribute_name = ?";

        try {
            log.debug("Fetching instance attribute mapping for attributeName: {}", attributeName);
            return jdbcTemplate.query(query,
                    new BeanPropertyRowMapper<>(CompInstanceAttributesBean.class),
                    attributeName);
        } catch (Exception e) {
            log.error("Error occurred while fetching instance attribute mappings for attributeName: {}. Details:", attributeName, e);
            return Collections.emptyList();
        }
    }

    /**
     * Retrieves paginated component instances for the specified account with optional search filtering.
     * @param accountId The account ID for which to fetch instances
     * @param searchTerm Optional search term to filter instances
     * @param pageable Pagination parameters
     * @return Paginated list of component instances for the specified account
     * @throws HealControlCenterException if database operations fail
     */
    public List<GetCompInstance> getComponentInstancesForAccount(int accountId, String searchTerm, int hostComponentTypeId, Pageable pageable) throws HealControlCenterException {
        String baseQuery = "SELECT id, name, identifier, host_id, host_name, host_identifier, " +
                "host_address, mst_component_id, component_name, mst_component_type_id, " +
                "component_type_name, mst_component_version_id, component_version_name, " +
                "common_version_id, common_version_name, status, discovery, is_DR, " +
                "updated_time FROM view_component_instance WHERE account_id = ? AND status = 1 AND is_cluster = 0 AND mst_component_type_id != ?";

        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.add(hostComponentTypeId);

        // Add search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(name) LIKE ? OR LOWER(component_name) LIKE ? OR LOWER(host_name) LIKE ?)";
            String searchPattern = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        // Apply pagination
        String paginatedQuery = PaginationUtils.applyPagination(baseQuery, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);

        try {
            log.debug("Fetching paginated component instances for accountId: {} with searchTerm: {}", accountId, searchTerm);
            return jdbcTemplate.query(paginatedQuery, new GetCompInstanceRowMapper(), params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching paginated component instances for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching paginated component instances.");
        }
    }

    /**
     * Counts total component instances for the specified account with optional search filtering.
     * @param accountId The account ID for which to count instances
     * @param searchTerm Optional search term to filter instances
     * @return Total count of component instances
     * @throws HealControlCenterException if database operations fail
     */
    public int countComponentInstancesForAccount(int accountId, String searchTerm, int hostComponentTypeId) throws HealControlCenterException {
        String baseQuery = "SELECT COUNT(*) FROM view_component_instance WHERE account_id = ? AND status = 1 AND is_cluster = 0 AND mst_component_type_id != ?";

        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.add(hostComponentTypeId);

        // Add search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(name) LIKE ? OR LOWER(component_name) LIKE ? OR LOWER(host_name) LIKE ?)";
            String searchPattern = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        try {
            log.debug("Counting component instances for accountId: {} with searchTerm: {}", accountId, searchTerm);
            return jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while counting component instances for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while counting component instances.");
        }
    }

    /**
     * Custom row mapper for GetCompInstance.
     */
    private static class GetCompInstanceRowMapper implements RowMapper<GetCompInstance> {
        @Override
        public GetCompInstance mapRow(ResultSet rs, int rowNum) throws SQLException {
            return GetCompInstance.builder()
                    .instanceId(String.valueOf(rs.getInt("id")))
                    .instanceName(rs.getString("name"))
                    .instanceIdentifier(rs.getString("identifier"))
                    .hostId(String.valueOf(rs.getInt("host_id")))
                    .hostName(rs.getString("host_name"))
                    .hostIdentifier(rs.getString("host_identifier"))
                    .hostAddress(Collections.singletonList(rs.getString("host_address")))
                    .componentId(rs.getInt("mst_component_id"))
                    .componentName(rs.getString("component_name"))
                    .componentTypeId(rs.getInt("mst_component_type_id"))
                    .componentTypeName(rs.getString("component_type_name"))
                    .componentVersionId(rs.getInt("mst_component_version_id"))
                    .componentVersionName(rs.getString("component_version_name"))
                    .commonVersionId(rs.getInt("common_version_id"))
                    .commonVersionName(rs.getString("common_version_name"))
                    .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                    .process(rs.getInt("discovery") == 0 ? "Manual" : "Auto")
                    .environment(String.valueOf(rs.getInt("is_DR")))
                    .lastDiscoveryRunTime(rs.getDate("updated_time").getTime())
                    .build();
        }
    }

    /**
     * Retrieves service-application mappings for the specified account.
     * @param accountId The account ID for which to fetch mappings
     * @return Map of service IDs to list of applications
     * @throws HealControlCenterException if database operations fail
     */
    public Map<Integer, List<IdNamePojo>> getServiceApplicationMappings(int accountId) throws HealControlCenterException {
        String query = "SELECT service_id, service_name, application_id, application_name " +
                "FROM view_application_service_mapping WHERE account_id = ?";

        try {
            List<Map<String, Object>> results = jdbcTemplate.queryForList(query, accountId);
            Map<Integer, List<IdNamePojo>> serviceAppMap = new HashMap<>();

            for (Map<String, Object> row : results) {
                Integer serviceId = (Integer) row.get("service_id");
                IdNamePojo application = IdNamePojo.builder()
                        .id((Integer) row.get("application_id"))
                        .name((String) row.get("application_name"))
                        .build();

                serviceAppMap.computeIfAbsent(serviceId, k -> new ArrayList<>()).add(application);
            }

            return serviceAppMap;
        } catch (Exception e) {
            log.error("Error occurred while fetching service-application mappings for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching service-application mappings.");
        }
    }

    /**
     * Retrieves agent mappings for the specified account.
     * @param accountId The account ID for which to fetch mappings
     * @return Map of instance IDs to list of agents
     * @throws HealControlCenterException if database operations fail
     */
    public Map<Integer, List<AgentDetails>> getInstanceAgentMappings(int accountId) throws HealControlCenterException {
        String query = "SELECT acim.comp_instance_id, a.id as agent_id, a.name as agent_name " +
                "FROM agent_comp_instance_mapping acim " +
                "JOIN agent a ON acim.agent_id = a.id " +
                "JOIN comp_instance ci ON acim.comp_instance_id = ci.id " +
                "WHERE ci.account_id = ? AND ci.status = 1";

        try {
            List<Map<String, Object>> results = jdbcTemplate.queryForList(query, accountId);
            Map<Integer, List<AgentDetails>> agentMap = new HashMap<>();

            for (Map<String, Object> row : results) {
                Integer instanceId = (Integer) row.get("comp_instance_id");
                AgentDetails agent = AgentDetails.builder()
                        .agentId((Integer) row.get("agent_id"))
                        .agentName((String) row.get("agent_name"))
                        .build();

                agentMap.computeIfAbsent(instanceId, k -> new ArrayList<>()).add(agent);
            }

            return agentMap;
        } catch (Exception e) {
            log.error("Error occurred while fetching instance-agent mappings for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching instance-agent mappings.");
        }
    }

    /**
     * Retrieves services for the specified instance.
     * @param accountId The account ID for which to fetch services
     * @param instanceIdentifiers The instance identifier for which to fetch services
     * @return Map of instance identifiers to list of services
     * @throws HealControlCenterException if database operations fail
     */
    public Map<String, List<IdNamePojo>> getServicesForInstances(int accountId, List<String> instanceIdentifiers) throws HealControlCenterException {
        String query = "SELECT DISTINCT ci.identifier, vcs.service_id, vcs.service_name " +
                "FROM view_cluster_services vcs " +
                "JOIN component_cluster_mapping ccm ON vcs.id = ccm.cluster_id " +
                "JOIN comp_instance ci ON ccm.comp_instance_id = ci.id " +
                "WHERE ci.account_id = ? AND ci.identifier IN (<instanceIdentifiers>) AND ci.status = 1";

        try {
            String inSql = String.join(",", Collections.nCopies(instanceIdentifiers.size(), "?"));
            List<Object> params = new ArrayList<>();
            params.add(accountId);
            params.addAll(instanceIdentifiers);

            List<Map<String, Object>> results = jdbcTemplate.queryForList(query.replace("<instanceIdentifiers>", inSql), params.toArray());
            Map<String, List<IdNamePojo>> instanceServiceMap = new HashMap<>();

            for (Map<String, Object> row : results) {
                String instanceIdentifier = (String) row.get("identifier");
                IdNamePojo service = IdNamePojo.builder()
                        .id((Integer) row.get("service_id"))
                        .name((String) row.get("service_name"))
                        .build();

                instanceServiceMap.computeIfAbsent(instanceIdentifier, k -> new ArrayList<>()).add(service);
            }

            return instanceServiceMap;
        } catch (Exception e) {
            log.error("Error occurred while fetching services for instances and accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching services for instances.");
        }
    }

    /**
     * Retrieves port attributes for the specified instances.
     * @param accountId The account ID for which to fetch port attributes
     * @param instanceIdentifiers The instance identifiers for which to fetch port attributes
     * @return Map of instance identifiers to list of port attributes
     * @throws HealControlCenterException if database operations fail
     */
    public Map<String, List<GetCompInstance.AttributeNameValue>> getPortAttributesForInstances(int accountId, List<String> instanceIdentifiers) throws HealControlCenterException {
        String query = "SELECT ci.identifier, mca.attribute_name, ciav.attribute_value " +
                "FROM comp_instance_attribute_values ciav " +
                "JOIN mst_common_attributes mca ON ciav.mst_common_attributes_id = mca.id " +
                "JOIN comp_instance ci ON ciav.comp_instance_id = ci.id " +
                "WHERE ci.account_id = ? AND ci.identifier IN (<instanceIdentifiers>) AND mca.attribute_name = 'MonitorPort'";

        try {
            String inSql = String.join(",", Collections.nCopies(instanceIdentifiers.size(), "?"));
            List<Object> params = new ArrayList<>();
            params.add(accountId);
            params.addAll(instanceIdentifiers);

            List<Map<String, Object>> results = jdbcTemplate.queryForList(query.replace("<instanceIdentifiers>", inSql), params.toArray());
            Map<String, List<GetCompInstance.AttributeNameValue>> instancePortMap = new HashMap<>();

            for (Map<String, Object> row : results) {
                String instanceIdentifier = (String) row.get("identifier");
                GetCompInstance.AttributeNameValue port = GetCompInstance.AttributeNameValue.builder()
                        .attributeName((String) row.get("attribute_name"))
                        .attributeValue((String) row.get("attribute_value"))
                        .build();

                instancePortMap.computeIfAbsent(instanceIdentifier, k -> new ArrayList<>()).add(port);
            }

            return instancePortMap;
        } catch (Exception e) {
            log.error("Error occurred while fetching port attributes for instances and accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching port attributes for instances.");
        }
    }

    /**
     * Retrieves paginated auto-discovery component instances for the specified account with optional search filtering.
     * @param accountId The account ID for which to fetch instances
     * @param searchTerm Optional search term to filter instances
     * @param pageable Pagination parameters
     * @return Paginated list of auto-discovery component instances for the specified account
     * @throws HealControlCenterException if database operations fail
     */
    public List<GetCompInstance> getAutoDiscoveryInstances(int accountId, String searchTerm, int hostComponentTypeId, Pageable pageable) throws HealControlCenterException {
        String baseQuery = "SELECT ap.process_name as instanceName, ap.process_identifier as instanceIdentifier, ah.hostname as hostName," +
                "ap.host_identifier as hostIdentifier, ada.attribute_name as attributeName,ada.attribute_value as attributeValue," +
                "ah.environment as isDR,ap.discovery_status as status,ah.discovery_status as hostStatus," +
                "ah.last_discovery_run_time as lastDiscoveryRunTime,ap.component_id as componentId,vc.component_name as componentName," +
                "ap.component_version_id as componentVersionId,vc.component_version_name as componentVersionName," +
                "vc.common_version_id as commonVersionId,vc.common_version_name as commonVersionName,ap.component_type_id as componentTypeId," +
                "vc.component_type_name as componentTypeName,vasm.service_id as serviceId,vasm.service_name as serviceName," +
                "vasm.service_identifier as serviceIdentifier,vasm.application_id as applicationId,vasm.application_name as applicationName," +
                "vasm.application_identifier as applicationIdentifier,ap.account_id as accountId,ah.is_ignored as isIgnored," +
                "ap.is_blacklisted as isBlacklisted " +
                "FROM autodisco_process ap JOIN autodisco_host ah ON ap.host_identifier = ah.host_identifier " +
                "LEFT JOIN autodisco_discovered_attributes ada ON ap.process_identifier = ada.discovered_attributes_identifier " +
                "AND LOWER(ada.entity_type) = 'compinstance' " +
                "LEFT JOIN view_components vc ON vc.component_id = ap.component_id AND vc.component_version_id = ap.component_version_id " +
                "AND vc.component_type_id = ap.component_type_id " +
                "LEFT JOIN autodisco_service_mapping asm ON ap.process_identifier = asm.service_mapping_identifier AND LOWER(asm.entity_type) = 'compinstance' " +
                "LEFT JOIN view_application_service_mapping vasm ON asm.service_identifier = vasm.service_identifier WHERE ah.is_ignored = 0 " +
                "AND ap.is_blacklisted = '0' AND ap.component_id != 0 AND ap.is_ignored = 0 AND ap.account_id = ? " +
                "AND ap.component_type_id != ?";

        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.add(hostComponentTypeId);

        // Add search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(ap.process_name) LIKE ? OR LOWER(vc.component_name) LIKE ? OR LOWER(ah.hostname) LIKE ?)";
            String searchPattern = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        // Apply pagination
        String paginatedQuery = PaginationUtils.applyPagination(baseQuery, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);

        try {
            log.debug("Fetching paginated auto-discovery instances for accountId: {} with searchTerm: {}", accountId, searchTerm);
            return jdbcTemplate.query(paginatedQuery, new AutoDiscoveryInstanceRowMapper(), params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching paginated auto-discovery instances for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching paginated auto-discovery instances.");
        }
    }

    /**
     * Counts total auto-discovery component instances for the specified account with optional search filtering.
     * @param accountId The account ID for which to count instances
     * @param searchTerm Optional search term to filter instances
     * @return Total count of auto-discovery component instances
     * @throws HealControlCenterException if database operations fail
     */
    public int countAutoDiscoveryInstances(int accountId, String searchTerm, int hostComponentTypeId) throws HealControlCenterException {
        String baseQuery = "SELECT COUNT(DISTINCT ap.process_identifier) FROM autodisco_process ap " +
                "JOIN autodisco_host ah ON ap.host_identifier = ah.host_identifier " +
                "LEFT JOIN view_components vc ON vc.component_id = ap.component_id AND vc.component_version_id = ap.component_version_id " +
                "AND vc.component_type_id = ap.component_type_id " +
                "WHERE ah.is_ignored = 0 AND ap.is_blacklisted = '0' AND ap.component_id != 0 AND ap.is_ignored = 0 AND ap.account_id = ? AND ap.component_type_id != ?";

        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.add(hostComponentTypeId);

        // Add search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(ap.process_name) LIKE ? OR LOWER(vc.component_name) LIKE ? OR LOWER(ah.hostname) LIKE ?)";
            String searchPattern = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        try {
            log.debug("Counting auto-discovery instances for accountId: {} with searchTerm: {}", accountId, searchTerm);
            return jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while counting auto-discovery instances for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while counting auto-discovery instances.");
        }
    }

    /**
     * Row mapper for auto-discovery instances
     */
    private static class AutoDiscoveryInstanceRowMapper implements RowMapper<GetCompInstance> {
        @Override
        public GetCompInstance mapRow(ResultSet rs, int rowNum) throws SQLException {

           return GetCompInstance.builder()
                            .instanceId(rs.getString("instanceIdentifier"))
                            .instanceName(rs.getString("instanceName"))
                            .instanceIdentifier(rs.getString("instanceIdentifier"))
                            .hostId(rs.getString("hostIdentifier"))
                            .hostName(rs.getString("hostName"))
                            .hostIdentifier(rs.getString("hostIdentifier"))
                            .componentId(rs.getInt("componentId"))
                            .componentName(rs.getString("componentName"))
                            .componentVersionId(rs.getInt("componentVersionId"))
                            .componentVersionName(rs.getString("componentVersionName"))
                            .commonVersionId(rs.getInt("commonVersionId"))
                            .commonVersionName(rs.getString("commonVersionName"))
                            .componentTypeId(rs.getInt("componentTypeId"))
                            .componentTypeName(rs.getString("componentTypeName"))
                            .service(Collections.singletonList(IdNamePojo.builder()
                                    .id(rs.getInt("serviceId"))
                                    .name(rs.getString("serviceName"))
                                    .identifier(rs.getString("serviceIdentifier"))
                                    .build()))
                            .application(Collections.singletonList(IdNamePojo.builder()
                                    .id(rs.getInt("applicationId"))
                                    .name(rs.getString("applicationName"))
                                    .identifier(rs.getString("applicationIdentifier"))
                                    .build()))
                            .port(new LinkedList<>(Collections.singletonList(GetCompInstance.AttributeNameValue.builder()
                                    .attributeName(rs.getString("attributeName"))
                                    .attributeValue(rs.getString("attributeValue"))
                                    .build())))
                            .status(DiscoveryStatus.valueOf(rs.getString("status")))
                            .process("Auto")
                            .environment(rs.getInt("isDR") == 1 ? "DR" : "Production")
                            .lastDiscoveryRunTime(rs.getTimestamp("lastDiscoveryRunTime") != null ? rs.getTimestamp("lastDiscoveryRunTime").getTime() : 0L)
                            .hostStatus(DiscoveryStatus.valueOf(rs.getString("hostStatus")))
                            .build();
        }
    }

    public MasterComponentTypeBean findByNameAndAccountId(String name, int accountId) {
        String sql = "SELECT id, name, description, is_custom as isCustom, status, " +
                "created_time as createdTime, updated_time as updatedTime, " +
                "user_details_id as userDetailsId, account_id as accountId " +
                "FROM mst_component_type " +
                "WHERE account_id IN (1, ?) AND LOWER(name) = LOWER(?)";

        List<MasterComponentTypeBean> results = jdbcTemplate.query(sql,
                new BeanPropertyRowMapper<>(MasterComponentTypeBean.class), accountId, name);

        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * Inserts a new component instance into the database.
     * @param componentInstance The component instance bean to insert
     * @return The generated ID of the inserted component instance
     * @throws HealControlCenterException if insertion fails
     */
    public int insertComponentInstance(ComponentInstanceBean componentInstance) throws HealControlCenterException {
        String sql = "INSERT INTO comp_instance (name, host_id, is_DR, is_cluster, mst_component_version_id, " +
                "created_time, updated_time, user_details_id, account_id, mst_component_id, mst_component_type_id, " +
                "discovery, host_address, identifier, mst_common_version_id, parent_instance_id, supervisor_id, status) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            int rowsAffected = jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, componentInstance.getName());
                ps.setInt(2, componentInstance.getHostId());
                ps.setInt(3, componentInstance.getIsDR());
                ps.setInt(4, componentInstance.getIsCluster());
                ps.setInt(5, componentInstance.getMstComponentVersionId());
                ps.setString(6, componentInstance.getCreatedTime());
                ps.setString(7, componentInstance.getUpdatedTime());
                ps.setString(8, componentInstance.getUserDetailsId());
                ps.setInt(9, componentInstance.getAccountId());
                ps.setInt(10, componentInstance.getMstComponentId());
                ps.setInt(11, componentInstance.getMstComponentTypeId());
                ps.setInt(12, componentInstance.getDiscovery());
                ps.setString(13, componentInstance.getHostAddress());
                ps.setString(14, componentInstance.getIdentifier());
                ps.setInt(15, componentInstance.getMstCommonVersionId());
                ps.setInt(16, componentInstance.getParentId());
                ps.setInt(17, componentInstance.getSupervisorId());
                ps.setInt(18, componentInstance.getStatus());
                return ps;
            }, keyHolder);

            if (rowsAffected > 0 && keyHolder.getKey() != null) {
                int generatedId = keyHolder.getKey().intValue();
                log.debug("Successfully inserted component instance with ID: {}", generatedId);
                return generatedId;
            } else {
                throw new HealControlCenterException("Failed to insert component instance - no rows affected");
            }
        } catch (Exception e) {
            log.error("Error occurred while inserting component instance: {}", e.getMessage(), e);
            throw new HealControlCenterException("Error occurred while inserting component instance: " + e.getMessage());
        }
    }

    /**
     * Inserts instance metadata for a component instance.
     * @param instanceId The component instance ID
     * @param userDetailsId The user who created the instance
     * @param createdTime The creation timestamp
     * @param updatedTime The update timestamp
     * @return The generated metadata ID
     * @throws HealControlCenterException if insertion fails
     */
    public int insertInstanceMetadata(int instanceId, String userDetailsId, String createdTime, String updatedTime) throws HealControlCenterException {
        String sql = "INSERT INTO instance_metadata (instance_id, environment_id, created_time, updated_time, user_details_id, status) " +
                "VALUES (?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            int rowsAffected = jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, instanceId);
                ps.setInt(2, 383); // Default environment ID as per original code
                ps.setString(3, createdTime);
                ps.setString(4, updatedTime);
                ps.setString(5, userDetailsId);
                ps.setInt(6, 1); // Active status
                return ps;
            }, keyHolder);

            if (rowsAffected > 0 && keyHolder.getKey() != null) {
                int generatedId = keyHolder.getKey().intValue();
                log.debug("Successfully inserted instance metadata with ID: {} for instance: {}", generatedId, instanceId);
                return generatedId;
            } else {
                throw new HealControlCenterException("Failed to insert instance metadata - no rows affected");
            }
        } catch (Exception e) {
            log.error("Error occurred while inserting instance metadata for instance {}: {}", instanceId, e.getMessage(), e);
            throw new HealControlCenterException("Error occurred while inserting instance metadata: " + e.getMessage());
        }
    }

    /**
     * Checks if a component instance with the given identifier already exists for the account.
     * @param identifier The component instance identifier
     * @param accountId The account ID
     * @return true if exists, false otherwise
     */
    public boolean componentInstanceExists(String identifier, int accountId) {
        String sql = "SELECT COUNT(*) FROM comp_instance WHERE identifier = ? AND account_id = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, identifier, accountId);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error occurred while checking component instance existence: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Gets component instance by identifier and account ID.
     * @param identifier The component instance identifier
     * @param accountId The account ID
     * @return ComponentInstanceBean if found, null otherwise
     */
    public ComponentInstanceBean getComponentInstanceByIdentifier(String identifier, int accountId) {
        String sql = "SELECT id, name, status, host_id as hostId, is_DR as isDR, is_cluster as isCluster, " +
                "mst_component_version_id as mstComponentVersionId, created_time as createdTime, " +
                "updated_time as updatedTime, user_details_id as userDetailsId, account_id as accountId, " +
                "mst_component_id as mstComponentId, mst_component_type_id as mstComponentTypeId, " +
                "discovery, host_address as hostAddress, identifier, mst_common_version_id as mstCommonVersionId, " +
                "parent_instance_id as parentId, supervisor_id as supervisorId " +
                "FROM comp_instance WHERE identifier = ? AND account_id = ?";

        try {
            List<ComponentInstanceBean> results = jdbcTemplate.query(sql,
                    new BeanPropertyRowMapper<>(ComponentInstanceBean.class), identifier, accountId);
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance by identifier: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Checks if host address and environment combination exists.
     * @param accountId The account ID
     * @param hostAddress The host address
     * @param isDR The environment/DR value
     * @return true if exists, false otherwise
     */
    public boolean checkHostAddressEnvironmentExists(int accountId, String hostAddress, int isDR) {
        String sql = "SELECT COUNT(*) FROM comp_instance WHERE account_id = ? AND host_address = ? AND is_DR = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress, isDR);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error checking host address environment existence: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Checks if host address and monitor port combination exists.
     * @param accountId The account ID
     * @param hostAddress The host address
     * @param monitorPort The monitor port
     * @return true if exists, false otherwise
     */
    public boolean checkHostAddressPortExists(int accountId, String hostAddress, String monitorPort) {
        String sql = "SELECT COUNT(*) FROM comp_instance ci " +
                "JOIN comp_instance_attribute_values ciav ON ci.id = ciav.comp_instance_id " +
                "JOIN mst_common_attributes mca ON ciav.mst_common_attributes_id = mca.id " +
                "WHERE ci.account_id = ? AND ci.host_address = ? AND mca.attribute_name = 'MonitorPort' AND ciav.attribute_value = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress, monitorPort);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error checking host address port existence: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Checks if host address exists.
     * @param accountId The account ID
     * @param hostAddress The host address
     * @return true if exists, false otherwise
     */
    public boolean checkHostAddressExists(int accountId, String hostAddress) {
        String sql = "SELECT COUNT(*) FROM comp_instance WHERE account_id = ? AND host_address = ? AND mst_component_type_id = 1";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error checking host address existence: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Gets instance cluster service details.
     * @param serviceId The service ID
     * @param accountId The account ID
     * @return List of instance cluster service details
     */
    public List<com.heal.controlcenter.beans.InstanceClusterServiceBean> getInstanceClusterServiceDetails(int serviceId, int accountId) {
        String sql = "SELECT ci.mst_component_id as clusterComponentId, ci.mst_component_type_id as clusterComponentTypeId, " +
                "ci.mst_common_version_id as clusterCommonVersionId " +
                "FROM comp_instance ci " +
                "JOIN tag_mapping tm ON ci.id = tm.object_id " +
                "WHERE tm.tag_details_id = (SELECT id FROM tag_details WHERE name = 'Service') " +
                "AND tm.object_ref_table = 'comp_instance' AND tm.tag_value = ? AND ci.account_id = ?";

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                com.heal.controlcenter.beans.InstanceClusterServiceBean bean = new com.heal.controlcenter.beans.InstanceClusterServiceBean();
                bean.setClusterComponentId(rs.getInt("clusterComponentId"));
                bean.setClusterComponentTypeId(rs.getInt("clusterComponentTypeId"));
                bean.setClusterCommonVersionId(rs.getInt("clusterCommonVersionId"));
                return bean;
            }, String.valueOf(serviceId), accountId);
        } catch (Exception e) {
            log.error("Error getting instance cluster service details: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets instance details by host address.
     * Original JDBI query: "select count(*) from comp_instance where account_id=:accountId and host_address=:hostAddress and is_DR=:isDR"
     * @param accountId The account ID
     * @param hostAddress The host address
     * @param isDR The environment/DR value
     * @return Count of instances or -1 on error
     */
    public int getInstanceDetailsByHostAddress(int accountId, String hostAddress, int isDR) {
        String sql = "SELECT COUNT(*) FROM comp_instance WHERE account_id = ? AND host_address = ? AND is_DR = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress, isDR);
            return count != null ? count : -1;
        } catch (Exception e) {
            log.error("Error getting instance details by host address: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Checks host address monitor port existence.
     * Original JDBI query: "select count(*) from comp_instance ci,comp_instance_attribute_values ciav,mst_common_attributes mca where ci.id=ciav.comp_instance_id and ciav.mst_common_attributes_id=mca.id and ci.account_id=:accountId and ci.host_address=:hostAddress and mca.attribute_name='MonitorPort' and ciav.attribute_value=:monitorPort"
     * @param accountId The account ID
     * @param hostAddress The host address
     * @param monitorPort The monitor port
     * @return Count of instances or -1 on error
     */
    public int checkHostAddressMonitorPortExistance(int accountId, String hostAddress, String monitorPort) {
        String sql = "SELECT COUNT(*) FROM comp_instance ci, comp_instance_attribute_values ciav, mst_common_attributes mca " +
                "WHERE ci.id = ciav.comp_instance_id AND ciav.mst_common_attributes_id = mca.id " +
                "AND ci.account_id = ? AND ci.host_address = ? AND mca.attribute_name = 'MonitorPort' AND ciav.attribute_value = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress, monitorPort);
            return count != null ? count : -1;
        } catch (Exception e) {
            log.error("Error checking host address monitor port existence: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Checks host address existence.
     * Original JDBI query: "select count(*) from comp_instance where account_id=:accountId and host_address=:hostAddress and mst_component_type_id=1"
     * @param accountId The account ID
     * @param hostAddress The host address
     * @return Count of instances
     */
    public int checkHostAddressExistance(int accountId, String hostAddress) {
        String sql = "SELECT COUNT(*) FROM comp_instance WHERE account_id = ? AND host_address = ? AND mst_component_type_id = 1";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Error checking host address existence: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * Gets instance cluster service details (POJO version).
     * Original JDBI query: "select ci.mst_component_id clusterComponentId,ci.mst_component_type_id clusterComponentTypeId,ci.mst_common_version_id clusterCommonVersionId from comp_instance ci,tag_mapping tm where ci.id=tm.object_id and tm.tag_details_id=(select id from tag_details where name='Service') and tm.object_ref_table='comp_instance' and tm.tag_value=:serviceId and ci.account_id=:accountId"
     * @param serviceId The service ID
     * @param accountId The account ID
     * @return List of instance cluster service POJOs
     */
    public List<com.heal.controlcenter.beans.InstanceClusterServicePojo> getInstanceClusterServiceDetailsPojo(int serviceId, int accountId) {
        String sql = "SELECT ci.mst_component_id as clusterComponentId, ci.mst_component_type_id as clusterComponentTypeId, " +
                "ci.mst_common_version_id as clusterCommonVersionId " +
                "FROM comp_instance ci, tag_mapping tm " +
                "WHERE ci.id = tm.object_id AND tm.tag_details_id = (SELECT id FROM tag_details WHERE name = 'Service') " +
                "AND tm.object_ref_table = 'comp_instance' AND tm.tag_value = ? AND ci.account_id = ?";

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                com.heal.controlcenter.beans.InstanceClusterServicePojo pojo = new com.heal.controlcenter.beans.InstanceClusterServicePojo();
                pojo.setClusterComponentId(rs.getInt("clusterComponentId"));
                pojo.setClusterComponentTypeId(rs.getInt("clusterComponentTypeId"));
                pojo.setClusterCommonVersionId(rs.getInt("clusterCommonVersionId"));
                return pojo;
            }, String.valueOf(serviceId), accountId);
        } catch (Exception e) {
            log.error("Error getting instance cluster service details: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Saves component instance to database.
     * @param bean Component instance bean
     * @return Generated instance ID
     */
    @Transactional
    public int saveComponentInstance(ComponentInstanceBean bean) {
        String sql = "INSERT INTO comp_instance (name, identifier, status, host_id, is_DR, is_cluster, " +
                "mst_component_version_id, created_time, updated_time, user_details_id, account_id, " +
                "mst_component_id, mst_component_type_id, discovery, host_address, mst_common_version_id, " +
                "parent_instance_id, supervisor_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, bean.getName());
                ps.setString(2, bean.getIdentifier());
                ps.setInt(3, bean.getStatus());
                ps.setInt(4, bean.getHostId());
                ps.setInt(5, bean.getIsDR());
                ps.setInt(6, bean.getIsCluster());
                ps.setInt(7, bean.getMstComponentVersionId());
                ps.setString(8, bean.getCreatedTime());
                ps.setString(9, bean.getUpdatedTime());
                ps.setString(10, bean.getUserDetailsId());
                ps.setInt(11, bean.getAccountId());
                ps.setInt(12, bean.getMstComponentId());
                ps.setInt(13, bean.getMstComponentTypeId());
                ps.setInt(14, bean.getDiscovery());
                ps.setString(15, bean.getHostAddress());
                ps.setInt(16, bean.getMstCommonVersionId());
                ps.setInt(17, bean.getParentId());
                ps.setInt(18, bean.getSupervisorId());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            return key != null ? key.intValue() : -1;

        } catch (Exception e) {
            log.error("Error saving component instance: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Saves component instance attribute.
     * @param attribute Component instance attribute bean
     * @return Generated attribute ID
     */
    @Transactional
    public int saveComponentInstanceAttribute(CompInstanceAttributesBean attribute) {
        String sql = "INSERT INTO comp_instance_attribute_values (comp_instance_id, mst_common_attributes_id, " +
                "attribute_value, user_details_id, created_time, updated_time) " +
                "VALUES (?, ?, ?, ?, NOW(), NOW())";

        try {
            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, attribute.getCompInstanceId());
                ps.setInt(2, attribute.getMstCommonAttributesId());
                ps.setString(3, attribute.getAttributeValue());
                ps.setString(4, attribute.getUserDetailsId());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            return key != null ? key.intValue() : -1;

        } catch (Exception e) {
            log.error("Error saving component instance attribute: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Saves agent instance mapping.
     * @param instanceId Component instance ID
     * @param agentId Agent ID
     * @return Generated mapping ID
     */
    @Transactional
    public int saveAgentInstanceMapping(int instanceId, int agentId) {
        String sql = "INSERT INTO agent_comp_instance_mapping (agent_id, comp_instance_id, created_time, updated_time) " +
                "VALUES (?, ?, NOW(), NOW())";

        try {
            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, agentId);
                ps.setInt(2, instanceId);
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            return key != null ? key.intValue() : -1;

        } catch (Exception e) {
            log.error("Error saving agent instance mapping: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Gets the mst_common_attributes_id by attribute name.
     * @param attributeName The attribute name
     * @return The attribute ID or -1 if not found
     */
    public int getMstCommonAttributesIdByName(String attributeName) {
        String sql = "SELECT id FROM mst_common_attributes WHERE attribute_name = ?";

        try {
            Integer id = jdbcTemplate.queryForObject(sql, Integer.class, attributeName);
            return id != null ? id : -1;
        } catch (Exception e) {
            log.error("Error getting mst_common_attributes_id for attribute name: {}", attributeName, e);
            return -1;
        }
    }

    /**
     * Gets host instance details - exact conversion from appsone-controlcenter JDBI to JDBC.
     * Original JDBI query from appsone-controlcenter BindInDataService.getHostInstanceId:
     * @SqlQuery("select distinct ci1.id hostInstanceId ,ci1.name hostInstanceName,  ci1.is_DR isDR from " +
     *           "component_cluster_mapping ccm, comp_instance ci1, comp_instance ci2  where ci1.status = 1 and " +
     *           "ci1.mst_component_type_id = 1 and  ccm.comp_instance_id=ci1.id and ccm.cluster_id=ci2.id and " +
     *           " ci2.is_cluster= 1 and ci1.is_cluster = 0 and ci1.mst_component_type_id = :hostCompTypeId and  " +
     *           "ci1.host_address=:hostAddress and ci1.account_id =:accId and ci1.is_DR=:isDR")
     * @param hostCompTypeId Host component type ID
     * @param hostAddress Host address
     * @param accountId Account ID
     * @param isDR Environment/DR flag
     * @return List of HostInstanceDetails matching original appsone-controlcenter
     */
    public List<HostInstanceDetails> getHostInstanceId(int hostCompTypeId, String hostAddress, int accountId, int isDR) {
        // Exact conversion of original JDBI query to JDBC
        String sql = "SELECT DISTINCT ci1.id AS hostInstanceId, ci1.name AS hostInstanceName, ci1.is_DR AS isDR " +
                "FROM component_cluster_mapping ccm, comp_instance ci1, comp_instance ci2 " +
                "WHERE ci1.status = 1 AND ccm.comp_instance_id = ci1.id " +
                "AND ccm.cluster_id = ci2.id AND ci2.is_cluster = 1 AND ci1.is_cluster = 0 " +
                "AND ci1.mst_component_type_id = ? AND ci1.host_address = ? AND ci1.account_id = ? AND ci1.is_DR = ?";

        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(HostInstanceDetails.class),
                    hostCompTypeId, hostAddress, accountId, isDR);
        } catch (Exception e) {
            log.error("Error getting host instance details: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets component instance by ID - equivalent to getComponentInstanceById from appsone-controlcenter.
     * @param instanceId Instance ID
     * @return ViewComponentInstanceBean if found, null otherwise
     */
    public ViewComponentInstanceBean getComponentInstanceById(int instanceId) {
        String sql = "SELECT id, name, status, identifier, host_id as hostId, " +
                "is_cluster as isCluster, account_id as accountId, user_details_id as userDetailsId, " +
                "discovery, host_address as hostAddress, is_DR as isDR, " +
                "mst_component_id as mstComponentId, mst_component_type_id as mstComponentTypeId, " +
                "mst_component_version_id as mstComponentVersionId, " +
                "common_version_id as commonVersionId, created_time as createdTime, " +
                "updated_time as updatedTime, parent_instance_id as parentInstanceId, " +
                "supervisor_id as supervisorId " +
                "FROM comp_instance " +
                "WHERE id = ?";

        @SqlQuery("select ccm.cluster_id clusterId, identifier clusterIdentifier, c.name clusterName, c.status status, c.host_id hostId, c.is_DR isDR, c.is_cluster isCluster, c.mst_component_version_id componentVersionId, c.created_time createdTime, c.updated_time updatedTime, c.user_details_id lastModifiedBy, c.account_id accountId, c.mst_component_id componentId, c.mst_component_type_id componentTypeId, c.discovery discovery, c.host_address hostAddress, c.mst_common_version_id commonVersionId , c.parent_instance_id parentInstanceId, c.supervisor_id supervisorId from component_cluster_mapping ccm," +
                " comp_instance c where ccm.cluster_id=c.id and comp_instance_id = :instanceId ")
        ClusterInstancePojo getClusterInstanceMapping(@Bind("instanceId") int instanceId);

        try {
            List<ViewComponentInstanceBean> results = jdbcTemplate.query(sql,
                    new BeanPropertyRowMapper<>(ViewComponentInstanceBean.class), instanceId);
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting component instance by ID: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Gets cluster instance mapping - exact conversion from appsone-controlcenter JDBI to JDBC.
     * Original JDBI query from appsone-controlcenter BindInDataService.getClusterInstanceMapping:
     * @SqlQuery("select ccm.cluster_id clusterId, identifier clusterIdentifier, c.name clusterName, c.status status,
     *           c.host_id hostId, c.is_DR isDR, c.is_cluster isCluster, c.mst_component_version_id componentVersionId,
     *           c.created_time createdTime, c.updated_time updatedTime, c.user_details_id lastModifiedBy,
     *           c.account_id accountId, c.mst_component_id componentId, c.mst_component_type_id componentTypeId,
     *           c.discovery discovery, c.host_address hostAddress, c.mst_common_version_id commonVersionId,
     *           c.parent_instance_id parentInstanceId, c.supervisor_id supervisorId from component_cluster_mapping ccm,
     *           comp_instance c where ccm.cluster_id=c.id and comp_instance_id = :instanceId")
     * @param instanceId Component instance ID
     * @return ClusterInstancePojo matching original appsone-controlcenter
     */
    public ClusterInstancePojo getClusterInstanceMapping(int instanceId) {
        // Exact conversion of original JDBI query to JDBC
        String sql = "SELECT ccm.cluster_id AS clusterId, c.identifier AS clusterIdentifier, c.name AS clusterName, " +
                "c.status AS status, c.host_id AS hostId, c.is_DR AS isDR, c.is_cluster AS isCluster, " +
                "c.mst_component_version_id AS componentVersionId, c.created_time AS createdTime, " +
                "c.updated_time AS updatedTime, c.user_details_id AS lastModifiedBy, c.account_id AS accountId, " +
                "c.mst_component_id AS componentId, c.mst_component_type_id AS componentTypeId, " +
                "c.discovery AS discovery, c.host_address AS hostAddress, c.mst_common_version_id AS commonVersionId, " +
                "c.parent_instance_id AS parentInstanceId, c.supervisor_id AS supervisorId " +
                "FROM component_cluster_mapping ccm, comp_instance c " +
                "WHERE ccm.cluster_id = c.id AND ccm.comp_instance_id = ?";

        try {
            List<ClusterInstancePojo> results = jdbcTemplate.query(sql,
                    new BeanPropertyRowMapper<>(ClusterInstancePojo.class), instanceId);
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting cluster instance mapping: {}", e.getMessage(), e);
            return null;
        }
    }
}
